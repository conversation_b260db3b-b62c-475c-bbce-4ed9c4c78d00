@page
@model LeaderSurvey.Pages.SurveysModel
@{
    ViewData["Title"] = "Surveys";
}
@Html.AntiForgeryToken()

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="page-title mb-0"><i class="bi bi-card-checklist"></i> Surveys Management</h2>
    <a href="/NewSurvey" class="cfa-btn cfa-btn-primary">
        <i class="bi bi-plus-circle"></i> Create New Survey
    </a>
</div>

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i> @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="container">
    <div class="filters-section">
        <div class="row">
            <div class="col-md-4">
                <div class="filter-group">
                    <label class="form-label">Date Range</label>
                    <div class="date-range-buttons mb-2">
                        <button type="button" class="date-range-btn active" data-range="all">All</button>
                        <button type="button" class="date-range-btn" data-range="current-month">Current Month</button>
                        <button type="button" class="date-range-btn" data-range="last-3-months">Last 3 Months</button>
                        <button type="button" class="date-range-btn" data-range="custom">Custom</button>
                    </div>
                    <div id="customDateRange" class="row" style="display: none;">
                        <div class="col-6">
                            <input type="date" id="startDate" class="form-control" placeholder="Start Date">
                        </div>
                        <div class="col-6">
                            <input type="date" id="endDate" class="form-control" placeholder="End Date">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="filter-group">
                    <label class="form-label">Area</label>
                    <select id="areaFilter" class="cfa-select" onchange="applyFilters()">
                        <option value="">All Areas</option>
                        <option value="Drive">Drive</option>
                        <option value="Front">Front</option>
                        <option value="Kitchen">Kitchen</option>
                        <option value="Hospitality">Hospitality</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="filter-group">
                    <label class="form-label">Leader</label>
                    <select id="leaderFilter" class="cfa-select" onchange="applyFilters()">
                        <option value="">All Leaders</option>
                        @foreach (var leader in Model.Leaders)
                        {
                            <option value="@leader.Id">@leader.Name</option>
                        }
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table id="surveysTable" class="table table-cfa">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Leader</th>
                    <th>Area</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var survey in Model.Surveys)
                {
                    <tr data-id="@survey.Id">
                        <td>@survey.Name</td>
                        <td data-leader-id="@survey.Leader?.Id">@survey.Leader?.Name</td>
                        <td>
                            <span class="badge area-badge <EMAIL>()">@survey.Area</span>
                        </td>
                        <td>@(survey.MonthYear.HasValue ? survey.MonthYear.Value.ToString("MMMM yyyy") : "")</td>
                        <td>
                            <span class="status-badge <EMAIL>()">
                                @survey.Status
                            </span>
                        </td>
                        <td>
                            <a href="/TakeSurvey?surveyId=@survey.Id" class="cfa-btn cfa-btn-sm me-1 @(survey.Status.Equals("Completed", StringComparison.OrdinalIgnoreCase) ? "disabled" : "")" style="background-color: #28a745; color: white;" @(survey.Status.Equals("Completed", StringComparison.OrdinalIgnoreCase) ? "aria-disabled='true'" : "")>
                                <i class="bi bi-check2-square"></i> Take Survey
                            </a>
                            <a href="/EditSurvey?id=@survey.Id&viewMode=true" class="cfa-btn cfa-btn-sm me-1">
                                <i class="bi bi-eye"></i> View
                            </a>
                            <a href="/EditSurvey?id=@survey.Id" class="cfa-btn cfa-btn-sm me-1">
                                <i class="bi bi-pencil-square"></i> Edit
                            </a>
                            <button class="cfa-btn cfa-btn-sm cfa-btn-outline"
                                    onclick="deleteSurvey(@survey.Id, '@survey.Name.Replace("'", "\'")')"
                                    style="border-color: #dc3545; color: #dc3545 !important;">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <div id="categoryStats" class="category-stats-container mt-4" style="display: none;">
        <h4>Category Statistics</h4>
        <div class="row">
            @foreach (var category in Model.Categories)
            {
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="category-card <EMAIL>()" data-category-id="@category.Id">
                        <div class="category-header d-flex justify-content-between align-items-center">
                            <h5 class="category-name mb-0">@category.Name</h5>
                            <button class="btn btn-sm btn-link category-expand-btn">
                                <i class="bi bi-chevron-down"></i>
                            </button>
                        </div>
                        <div class="category-stats mt-3">
                            <div class="stat-item">
                                <span class="stat-label">Yes/No Questions:</span>
                                <span class="stat-value yesno-count">0</span>
                                <span class="stat-percentage yesno-percentage">0%</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Score Questions:</span>
                                <span class="stat-value score-count">0</span>
                                <span class="stat-average score-average">0.0</span>
                            </div>
                        </div>
                        <div class="category-questions mt-3" style="display: none;">
                            <!-- Questions will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- The modal viewer has been replaced with direct page navigation to EditSurvey -->

@section Scripts {
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/surveys.js"></script>
}

@section Styles {
    <link rel="stylesheet" href="~/css/survey-categories.css" />
}
