WebAssembly is supported in this environment
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 WASM SDK loaded in 598ms
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 State version: 71
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Mv (to version 3) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Lm (to version 4) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Of (to version 5) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator wy (to version 6) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Iv (to version 7) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Tv (to version 8) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Ov (to version 9) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Gp (to version 10) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Yp (to version 11) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator ig (to version 12) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator ag (to version 13) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator lg (to version 14) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator dg (to version 15) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator gg (to version 16) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator yg (to version 17) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator wg (to version 18) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Ig (to version 19) should migrate: false - up
chrome-extension://nngceckbapebfimnlniiiahkandclblb/background.js:2 Migrator Eg (to version 20) should migrate: false - up
Navigated to http://localhost:8427/Login
content-scripts.js:1 CONTENT_SHELL: Page is excluded. Skipping shell protection.
content-scripts.js:1 TSS: excluded result:  true
content-scripts.js:1 SCHJK: Search Hijacking notification feature flag is enabled. true
content-scripts.js:1 DFP: Breach notification feature flag is enabled. true
content-scripts.js:1 Content Script Bridge: Unknown message type,  MSG_CHECK_DOMAIN_ALLOW_LIST_RESPONSE
injection-tss-mv3.js:1 TSS: Domain is in allow list, skipping TSS setup.
browserLink:18 CSS Hot Reload ignoring http://localhost:8427/lib/bootstrap/css/bootstrap.min.css because it was inaccessible or had more than 7000 rules.
browserLink:18 CSS Hot Reload ignoring https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css because it was inaccessible or had more than 7000 rules.
chrome-error://chromewebdata/:1  Failed to load resource: the server responded with a status of 400 ()Understand this error
Navigated to chrome-error://chromewebdata/