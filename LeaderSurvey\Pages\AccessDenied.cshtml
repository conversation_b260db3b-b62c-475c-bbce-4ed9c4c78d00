@page
@model LeaderSurvey.Pages.AccessDeniedModel
@{
    ViewData["Title"] = "Access Denied";
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Leader Survey</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        body {
            background: linear-gradient(135deg, #e51636 0%, #c41230 50%, #a00e28 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .access-denied-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            padding: 3rem 2.5rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .error-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #dc3545, #c82333);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
        }

        .error-icon i {
            font-size: 3rem;
            color: white;
        }

        .error-title {
            color: #2c2c2c;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .error-message {
            color: #6c757d;
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .login-btn {
            background: linear-gradient(135deg, #e51636, #c41230);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            padding: 1rem 2rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #c41230, #a00e28);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(229, 22, 54, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="error-icon">
            <i class="bi bi-shield-x"></i>
        </div>
        <h1 class="error-title">Access Denied</h1>
        <p class="error-message">
            You don't have permission to access this page. Please sign in with an authorized account to continue.
        </p>
        <a href="/Login" class="login-btn">
            <i class="bi bi-box-arrow-in-right"></i>
            Sign In
        </a>
    </div>
</body>
</html>
