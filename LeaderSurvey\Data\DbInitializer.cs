using LeaderSurvey.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace LeaderSurvey.Data
{
    public static class DbInitializer
    {
        public static async Task InitializeAsync(ApplicationDbContext context, UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager, IConfiguration configuration)
        {
            // Ensure database is created and migrations are applied
            await context.Database.MigrateAsync();

            // Seed roles
            if (!await roleManager.RoleExistsAsync("Admin"))
            {
                await roleManager.CreateAsync(new IdentityRole("Admin"));
            }

            // Seed admin user
            var adminEmail = configuration["AdminUser:Email"];
            if (await userManager.FindByEmailAsync(adminEmail) == null)
            {
                var adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    EmailConfirmed = true
                };

                var password = configuration["AdminUser:Password"];
                var result = await userManager.CreateAsync(adminUser, password);

                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }

            // Seed question categories if they don't exist
            if (!await context.QuestionCategories.AnyAsync())
            {
                var categories = new List<QuestionCategory>
                {
                    new QuestionCategory { Name = "Mechanical", Description = "Questions related to mechanical skills and processes" },
                    new QuestionCategory { Name = "Character", Description = "Questions related to character traits and behaviors" },
                    new QuestionCategory { Name = "Theory", Description = "Questions related to theoretical knowledge and understanding" }
                };

                await context.QuestionCategories.AddRangeAsync(categories);
                await context.SaveChangesAsync();
            }
        }
    }
}