:root {
    --primary-color: #E4002B; /* Chick-fil-A red */
    --secondary-color: #DD0031; /* Slightly darker red for hover states */
    --accent-color: #000000; /* Black for accents */
    --text-color: #000000; /* Black text */
    --light-bg: #FFFFFF; /* White background */
    --dark-bg: #E4002B; /* Red background for footer/header */
    --light-red-bg: #FEF2F4; /* Very light red for backgrounds */
    --menu-width: 250px; /* Width of the sidebar menu */
    --scrollbar-width: 0px; /* Force scrollbar width to 0 */
}

html {
    font-size: 14px;
    position: relative;
    min-height: 100%;
    overflow-y: scroll; /* Always show the vertical scrollbar */
    padding-right: 0; /* Reset padding */
}

html.side-menu-active {
    padding-right: var(--scrollbar-width); /* Add padding when the side menu is active */
}

body {
    margin-bottom: 60px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: var(--light-bg);
    padding-top: 20px; /* Adjusted for new header size */
}

/* Ensure the navbar stays fixed and doesn't jump */
.navbar {
    padding-right: var(--scrollbar-width);
}

html.side-menu-active .navbar {
    padding-right: calc(1rem + var(--scrollbar-width));
}

/* Remove any transforms that might cause content shift */
body.menu-open .container,
body.menu-open .navbar,
.container,
.navbar {
    transform: none !important;
    transition: padding 0.3s ease-in-out;
}

/* Add this to calculate scrollbar width on load */
@media (min-width: 768px) {
    html {
        font-size: 16px;
    }
}

/* Main header and navigation */
.navbar {
    background-color: var(--dark-bg) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    padding: 0.5rem 1rem;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1030;
    transition: all 0.3s ease-in-out;
    display: flex;
    align-items: center;
}

/* Container with reduced padding */
.container {
    padding-left: 3px;    /* Reduced by 75% from 12px */
    padding-right: 3px;   /* Reduced by 75% from 12px */
    padding-top: 1rem;
    transition: none;
    margin-left: auto !important;
}

header {
    height: 20px; /* Consistent height with navbar */
}

/* Move Leader Survey brand to the right */
.navbar-brand {
    color: white !important;
    font-weight: 600;
    margin-left: auto; /* Change from 60px to auto to push to right */
    margin-right: 20px; /* Add 20px margin from right edge */
    display: flex;
    align-items: center;
    height: 100%;
    font-size: 0.9rem; /* Adjusted for smaller header */
}
/* Hamburger Menu Positioning*/
.hamburger-menu {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 1.5rem; /* Adjusted for smaller header */
    height: 1.5rem; /* Adjusted for smaller header */
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    cursor: pointer;
    padding: 0.2rem; /* Adjusted for smaller header */
    margin-right: 0;
    z-index: 1050;
    position: relative;
}

/* Fixed hamburger menu that's always visible at the top left */
.fixed-hamburger {
    position: fixed;
    top: 2px; /* Adjusted for smaller header */
    left: 12px;
    z-index: 1100;
}

/* Hamburger menu open state */
.hamburger-menu.open .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-menu.open .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger-menu.open .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

.hamburger-line {
    width: 100%;
    height: 0.3rem;
    background-color: white;
    border-radius: 10px;
    transition: all 0.3s ease-in-out;
    position: relative;
    transform-origin: center;
    margin: 0.15rem 0;
}

/* Add new navigation styles */
.nav-links {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.2s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-item:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
}

.nav-item.active {
    background-color: var(--secondary-color);
    color: white;
    font-weight: 600;
}

.nav-item i {
    font-size: 1.1rem;
}

/* Remove menu-related styles */
body.menu-open .container,
body.menu-open .navbar {
    margin-left: 0 !important;
    transform: none !important;
}

/* Remove dimming overlay */
.menu-overlay {
    position: fixed; /* Fixed positioning */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent; /* Remove dimming */
    z-index: 1010; /* Below sidebar and hamburger */
    display: none;
}

    .menu-overlay.open {
        display: block;
    }
/* Pulsating animation keyframes */
@keyframes pulsate {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

/* When menu is open, adjust content */
body.menu-open .container,
body.menu-open .navbar {
    transform: none !important;
}

/* Ensure the sidebar menu stays on top without pushing content */
.sidebar-menu {
    position: fixed;
    top: 56px;
    left: 0;
    width: var(--menu-width);
    height: 100vh;
    background-color: var(--dark-bg);
    padding-top: 5px;
    transition: all 0.3s ease-in-out;
    z-index: 1020;
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
    transform: translateX(-100%);
}

/* Ensure container doesn't move */
.container {
    position: relative;
    transition: margin-left 0.3s ease-in-out;
    margin-left: 0;
}

/* Add margin when menu is open */
body.menu-open .container {
    margin-left: var(--menu-width);
}

/* Ensure navbar doesn't move */
.navbar {
    transition: margin-left 0.3s ease-in-out;
    margin-left: 0;
}

/* Add margin to navbar when menu is open */
body.menu-open .navbar {
    margin-left: var(--menu-width);
}

/* Remove the menu overlay since we're not overlapping content */
.menu-overlay {
    display: none !important;
}

/* ALL OTHER CSS BELOW IS UNCHANGED */

a {
    color: #0077cc;
}

.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

.border-top {
    border-top: 1px solid #e5e5e5;
}

.border-bottom {
    border-bottom: 1px solid #e5e5e5;
}

.box-shadow {
    box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy {
    font-size: 1rem;
    line-height: inherit;
}

.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    white-space: nowrap;
    line-height: 60px;
}

/* Add navigation states */
body.navigating {
    cursor: wait;
    pointer-events: none;
}

.sidebar-menu a {
    display: block;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    outline: none; /* Remove outline */
}

.sidebar-menu a:hover {
    background-color: var(--secondary-color); /* Darker red for hover */
    transform: translateX(5px);
    color: white;
}

.sidebar-menu a.active {
    background-color: var(--secondary-color); /* Darker red for active state */
    color: white;
    border-left: 4px solid white; /* White left border for active state */
    font-weight: 600;
}

/* Remove any conflicting styles */
.sidebar-menu ul li a {
    color: white;
    text-decoration: none;
    display: block;
}

.sidebar-menu ul li a.active {
    background-color: var(--secondary-color);
    color: white;
}

/* Remove focus outline */
.sidebar-menu a:focus {
    outline: none;
}

.sidebar-menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    transition: all 0.2s ease;
}

.sidebar-menu a:hover i {
    transform: scale(1.2);
}

.jumbotron {
    background-color: var(--light-red-bg);
    border-radius: 0.5rem;
    padding: 2rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(228, 0, 43, 0.2);
    margin-top: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

    .btn-primary:hover, .btn-primary:focus {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        color: white;
    }

.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

    .btn-success:hover, .btn-success:focus {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        color: white;
    }

.btn-info {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

    .btn-info:hover, .btn-info:focus {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        color: white;
    }

.btn-danger {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

    .card:hover {
        transform: translateY(-5px);
    }

.card-header {
    width: 1354px;
    height: 40.8px;
    padding: 8px 16px;
    margin: 0;
    border-width: 0 0 0.8px 0;
    box-sizing: border-box;
    background-color: var(--primary-color);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0 !important;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Ensure consistent sizing for both edit and new leader states */
.card-header #formTitle {
    font-size: 1rem;
    min-width: 120px;
    display: inline-block;
}

.card-header #formIcon {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.card-header > div {
    display: flex;
    align-items: center;
    min-width: 150px;
}

.table {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    overflow: hidden;
}

    .table thead th {
        background-color: var(--primary-color);
        color: white;
        border-bottom: none;
        font-weight: bold;
    }

.table-hover tbody tr:hover {
    background-color: rgba(228, 0, 43, 0.1);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(228, 0, 43, 0.25);
}

.form-group {
    margin-bottom: 1.5rem;
}

    .form-group label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--accent-color);
    }

/* Table styles */
.table-container {
    background-color: var(--light-bg);
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 2rem;
}

.table-cfa {
    width: 100%;
    margin-bottom: 0;
}

    .table-cfa thead th {
        background-color: var(--primary-color);
        color: white;
        font-weight: bold;
        padding: 1rem;
        border: none;
    }

    .table-cfa tbody td {
        padding: 1rem;
        border-bottom: 1px solid #f0f0f0;
        vertical-align: middle;
    }

    .table-cfa tbody tr:last-child td {
        border-bottom: none;
    }

    .table-cfa tbody tr:hover {
        background-color: var(--light-red-bg);
    }

/* Recent surveys section */
.recent-surveys {
    margin-top: 2rem;
}

.recent-surveys-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.recent-surveys-title {
    color: var(--primary-color);
    font-weight: 700;
    margin: 0;
}

.survey-card {
    background-color: var(--light-bg);
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

    .survey-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

.survey-card-title {
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.survey-card-area {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 600;
}

.survey-card-date {
    font-size: 0.85rem;
    color: #6c757d;
}

.survey-card-actions {
    margin-top: 1rem;
}

/* Alert messages */
.alert-cfa {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
}

.alert-cfa-primary {
    background-color: var(--light-red-bg);
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
}

.alert-cfa-warning {
    background-color: #FFF8E1;
    color: #FF8F00;
    border-left: 4px solid #FF8F00;
}

.alert-cfa-danger {
    background-color: #FFEBEE;
    color: #D32F2F;
    border-left: 4px solid #D32F2F;
}

.btn {
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.25rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

    a:hover {
        color: var(--secondary-color);
        text-decoration: none;
    }

.page-title {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    margin-top: 50px;    /* Decreased from 55px to 50px */
    font-weight: 700;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

/* CFA-style buttons and controls */
.cfa-btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white !important;
    border: none;
    border-radius: 30px;
    padding: 12px 25px;
    font-weight: bold;
    text-align: center;
    text-decoration: none;
    margin: 10px 5px;
    transition: all 0.3s ease;
    cursor: pointer;
}

    .cfa-btn:hover {
        background-color: var(--secondary-color);
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .cfa-btn i {
        margin-right: 8px;
    }

.cfa-btn-outline {
    background-color: transparent;
    color: var(--primary-color) !important;
    border: 2px solid var(--primary-color);
}

    .cfa-btn-outline:hover {
        background-color: var(--primary-color);
        color: white !important;
    }

.cfa-btn-sm {
    padding: 8px 15px;
    font-size: 0.875rem;
}

/* Add this after the .cfa-btn styles */
.card-body form button[type="submit"] {
    float: right;
    padding: 15px 35px;  /* Slightly bigger padding */
    font-size: 1.1rem;   /* Slightly bigger font */
    margin-right: 0;     /* Remove right margin */
    margin-top: 1rem;    /* Add some top spacing */
    border-radius: 30px; /* Match other CFA buttons */
}

/* Clear the float */
.card-body form::after {
    content: "";
    display: table;
    clear: both;
}

/* Search controls */
.search-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 1px solid #e0e0e0;
    border-radius: 30px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

    .search-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(228, 0, 43, 0.1);
    }

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Form field with add button */
.form-field-with-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

    .form-field-with-button .form-control {
        flex: 1;
    }

.add-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

    .add-button:hover {
        background-color: var(--secondary-color);
        transform: scale(1.1);
    }

/* Toggle switches */
.toggle-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

    input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }

.toggle-label {
    font-weight: 500;
}

main.pb-3 {
    width: calc(100% + 40px);  /* Makes it 20px wider on each side */
    margin-left: -20px;        /* Offsets the extra width to keep it centered */
    margin-right: -20px;
}

/* Question styles */
.bg-cfa-red {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.question-item {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: move;
    transition: all 0.3s ease;
    position: relative;
}

.question-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.question-number {
    background-color: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
}

.question-drag-handle {
    cursor: move;
    color: #ccc;
    margin-right: 1rem;
}

.question-drag-handle:hover {
    color: var(--primary-color);
}

.my-pleasure {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: var(--primary-color);
    font-weight: bold;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.question-item.deleting {
    animation: fadeOut 0.5s ease forwards;
}

@keyframes fadeOut {
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

.questions-sortable .sortable-ghost {
    opacity: 0.4;
    background-color: var(--light-red-bg);
}

.question-type-select {
    width: 150px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    padding: 0.5rem;
}

.number-input {
    width: 80px;
    text-align: center;
}

/* Survey Page Specific Styles */
.surveys-container {
    padding: 1rem;
    margin-top: 20px;
}

.filters-section {
    background-color: var(--light-red-bg);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-group {
    min-width: 200px;
}

.gap-3 {
    gap: 1rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.cfa-select {
    width: 100%;
    padding: 0.5rem;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    background-color: white;
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.cfa-select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(228, 0, 43, 0.1);
}

.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
    min-height: 38px;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    gap: 0.5rem;
}

.filter-tag i {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.filter-tag i:hover {
    transform: scale(1.2);
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.status-pending {
    background-color: #ffd700;
    color: #000;
}

.status-badge.status-completed {
    background-color: #28a745;
    color: #fff;
}

.status-badge.status-inprogress {
    background-color: #17a2b8;
    color: #fff;
}

.completion-date {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
    text-align: center;
}

/* Table sorting styles */
.sortable {
    position: relative;
    cursor: pointer;
    user-select: none;
}

.sort-arrows {
    display: inline-flex;
    flex-direction: column;
    margin-left: 0.5rem;
    line-height: 0.6;
}

.sort-arrows i {
    font-size: 0.7rem;
    color: #ccc;
    cursor: pointer;
    transition: color 0.2s ease;
}

.sort-arrows i:hover {
    color: #e51636;
}

.sort-arrows i.active {
    color: #e51636;
}

.sortable:hover {
    background-color: rgba(229, 22, 54, 0.1);
}

.survey-editor {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.survey-editor-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.survey-editor-content {
    padding: 1rem;
}

.survey-editor-actions {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.questions-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.questions-sortable {
    min-height: 50px;
    margin: 1rem 0;
}

.question-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.question-item:hover {
    background-color: #FDE8EC;
}

.actions-cell {
    white-space: nowrap;
    display: flex;
    gap: 0.5rem;
}

.cfa-btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.cfa-btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* Area filter badges */
.area-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
}

.area-filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.area-filter {
    border: none;
    background: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.area-filter:hover {
    background-color: var(--light-red-bg);
}

.area-filter.active {
    background-color: #f8f9fa;
}

.area-filter .area-badge {
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.area-filter:hover .area-badge,
.area-filter.active .area-badge {
    opacity: 1;
}

/* Area badge colors */
.area-drive {
    background-color: #4CAF50; /* Green */
}

.area-front {
    background-color: #FFEB3B; /* Yellow */
}

.area-kitchen {
    background-color: #9C27B0; /* Purple */
}

.area-hospitality {
    background-color: #2196F3; /* Blue */
}

.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 250px;
    max-width: 350px;
    animation: slideIn 0.15s ease-in;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Survey Viewer/Editor Modal Styles */
.survey-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.survey-viewer-content {
    position: relative;
    width: 90%;
    max-width: 800px;
    margin: 2rem auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.survey-viewer-header {
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.survey-viewer-body {
    padding: 1rem;
}

.tab-content {
    display: none;
    padding: 1rem 0;
}

.tab-content.active {
    display: block;
}

.nav-tabs .nav-link {
    cursor: pointer;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

/* Add these styles to your existing CSS */
.sortable-ghost {
    opacity: 0.5;
    background-color: #f8f9fa;
}

.drag-handle {
    color: #6c757d;
    cursor: move;
}

.drag-handle:hover {
    color: #495057;
}

.question-number {
    font-weight: bold;
    color: #495057;
}

.table-cfa td {
    vertical-align: middle;
}

/* Question type badges */
.badge.bg-primary {
    background-color: #e51636 !important;
}

.badge.bg-success {
    background-color: #00a6d6 !important;
}

/* TakeSurvey page styles */
.question-card {
    transition: all 0.3s ease;
}

.question-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Disabled Take Survey button */
.cfa-btn.disabled {
    background-color: #6c757d !important;
    opacity: 0.65;
    cursor: not-allowed;
    pointer-events: none;
    box-shadow: none;
}

.cfa-btn.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Score buttons styling */
.score-buttons-container {
    padding: 10px 15px;
}

.score-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
}

.score-btn {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    border-radius: 50%;
    transition: all 0.2s ease;
}

@media (max-width: 576px) {
    .score-btn {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
}

.btn-check:checked + .score-btn {
    transform: scale(1.15);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* Additional notes section styling */
.card-header .badge.bg-secondary {
    font-size: 0.75rem;
    font-weight: normal;
    padding: 0.35em 0.65em;
}

.form-range::-webkit-slider-thumb {
    background-color: var(--primary-color);
}

.form-range::-moz-range-thumb {
    background-color: var(--primary-color);
}

.form-range::-ms-thumb {
    background-color: var(--primary-color);
}

.btn-check:checked + .btn-outline-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-check:checked + .btn-outline-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.area-badge {
    padding: 0.5em 0.8em;
    font-size: 0.8em;
    font-weight: 600;
    border-radius: 4px;
}

.area-front {
    background-color: #FFEB3B; /* Yellow */
    color: black; /* Changed to black for better contrast with yellow */
}

.area-drive {
    background-color: #4CAF50; /* Green */
    color: white;
}

.area-kitchen {
    background-color: #9C27B0; /* Purple */
    color: white;
}

.area-hospitality {
    background-color: #2196F3; /* Blue */
    color: white;
}

/* Question type badges */
.question-type-badge .badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Instructions styling */
.card-body .small.text-muted {
    margin-bottom: 0.75rem;
    font-style: italic;
}

/* Question card styling */
.question-card .card-header {
    background-color: #f8f9fa;
    color: #000 !important;
    font-weight: 500;
}

.question-card .card-header strong {
    color: #000 !important;
    font-size: 1.05rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 0.25rem;
}
