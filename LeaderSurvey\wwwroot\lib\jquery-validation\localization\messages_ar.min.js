/*! jQuery Validation Plugin - v1.21.0 - 7/17/2024
 * https://jqueryvalidation.org/
 * Copyright (c) 2024 <PERSON><PERSON><PERSON>; Licensed MIT */
!function(a){"function"==typeof define&&define.amd?define(["jquery","../jquery.validate.min"],a):"object"==typeof module&&module.exports?module.exports=a(require("jquery")):a(jQuery)}(function(a){return a.extend(a.validator.messages,{required:"هذا الحقل إلزامي",remote:"يرجى تصحيح هذا الحقل للمتابعة",email:"رجاء إدخال عنوان بريد إلكتروني صحيح",url:"رجاء إدخال عنوان موقع إلكتروني صحيح",date:"رجاء إدخال تاريخ صحيح",dateISO:"رجاء إدخال تاريخ صحيح (ISO)",number:"رجاء إدخال عدد بطريقة صحيحة",digits:"رجاء إدخال أرقام فقط",creditcard:"رجاء إدخال رقم بطاقة ائتمان صحيح",equalTo:"رجاء إدخال نفس القيمة",extension:"رجاء إدخال ملف بامتداد موافق عليه",maxlength:a.validator.format("الحد الأقصى لعدد الحروف هو {0}"),minlength:a.validator.format("الحد الأدنى لعدد الحروف هو {0}"),rangelength:a.validator.format("عدد الحروف يجب أن يكون بين {0} و {1}"),range:a.validator.format("رجاء إدخال عدد قيمته بين {0} و {1}"),max:a.validator.format("رجاء إدخال عدد أقل من أو يساوي {0}"),min:a.validator.format("رجاء إدخال عدد أكبر من أو يساوي {0}"),step:a.validator.format("يرجى تقديم قيمة من مضاعفات {0}"),maxWords:a.validator.format("يرجى تقديم ما لا يزيد عن {0} كلمات"),minWords:a.validator.format("يرجى تقديم {0} كلمات على الأقل"),rangeWords:a.validator.format("يرجى تقديم ما بين {0} و{1} كلمة"),letterswithbasicpunc:"يرجى تقديم الحروف وعلامات الترقيم فقط",alphanumeric:"يرجى تقديم الحروف والأرقام والمسافات والتسطير فقط",lettersonly:"يرجى تقديم الحروف فقط",nowhitespace:"من فضلك لا تدخل المساحات البيضاء",ziprange:"يرجى تقديم الرمز البريدي بين 902xx-xxxx و905-xx-xxxx",integer:"يرجى تقديم رقم غير عشري موجب أو سالب",vinUS:"يرجى تقديم رقم تعريف المركبة (VIN)",dateITA:"يرجى تقديم تاريخ صالح",time:"يرجى تقديم وقت صالح بين 00:00 و23:59",phoneUS:"الرجاء تقديم رقم هاتف صالح",phoneUK:"الرجاء تقديم رقم هاتف صالح",mobileUK:"يرجى تقديم رقم هاتف محمول صالح",strippedminlength:a.validator.format("يرجى تقديم {0} حرفًا على الأقل"),email2:"يرجى تقديم عنوان بريد إلكتروني صالح",url2:"يرجى إدخال عنوان بريد إلكتروني صحيح",creditcardtypes:"يرجى تقديم رقم بطاقة ائتمان صالح",currency:"يرجى تقديم عملة صالحة",ipv4:"يرجى تقديم عنوان IP v4 صالح",ipv6:"يرجى تقديم عنوان IP v6 صالح",require_from_group:a.validator.format("يرجى تقديم ما لا يقل عن {0} من هذه الحقول"),nifES:"يرجى تقديم رقم TIN صالح",nieES:"يرجى تقديم رقم NIE صالح",cifES:"يرجى تقديم رقم CIF صالح",postalCodeCA:"يرجى تقديم رمز بريدي صالح",pattern:"التنسيق غير صالح"}),a});