﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Leader Survey</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="icon" type="image/x-icon" href="~/favicon.ico">
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <div id="notification-container"></div>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container">
                <div class="navbar-nav">
                    <a href="/Index" class="nav-item @(ViewContext.RouteData.Values["Page"]?.ToString() == "/Index" ? "active" : "")">
                        <i class="bi bi-house-fill"></i> Home
                    </a>
                    <a href="/Leaders" class="nav-item @(ViewContext.RouteData.Values["Page"]?.ToString() == "/Leaders" ? "active" : "")">
                        <i class="bi bi-people-fill"></i> Team
                    </a>
                    <a href="/Surveys" class="nav-item @(ViewContext.RouteData.Values["Page"]?.ToString() == "/Surveys" ? "active" : "")">
                        <i class="bi bi-card-checklist"></i> Surveys
                    </a>
                    <a href="/NewSurvey" class="nav-item @(ViewContext.RouteData.Values["Page"]?.ToString() == "/NewSurvey" ? "active" : "")">
                        <i class="bi bi-plus-circle"></i> New Survey
                    </a>
                    <a href="/Surveys" class="nav-item @(ViewContext.RouteData.Values["Page"]?.ToString() == "/TakeSurvey" ? "active" : "")">
                        <i class="bi bi-check2-square"></i> Take Survey
                    </a>

                </div>
                <a class="navbar-brand" asp-area="" asp-page="/Index">
                    <i class="bi bi-clipboard-data"></i> Leader Survey
                </a>
                @if (User.Identity.IsAuthenticated)
                {
                    <div class="navbar-nav ms-auto">
                        <a href="/Logout" class="nav-item logout-btn" title="Logout">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </div>
                }
            </div>
        </nav>
    </header>
    <div class="sidebar-menu">
        <ul>
            <li>
                <a href="/Index" class="@(ViewContext.RouteData.Values["Page"]?.ToString() == "/Index" ? "active" : "")">
                    <i class="bi bi-house-fill"></i> Home
                </a>
            </li>
            <li>
                <a href="/Leaders" class="@(ViewContext.RouteData.Values["Page"]?.ToString() == "/Leaders" ? "active" : "")">
                    <i class="bi bi-people-fill"></i> Team
                </a>
            </li>
            <li>
                <a href="/Surveys" class="@(ViewContext.RouteData.Values["Page"]?.ToString() == "/Surveys" ? "active" : "")">
                    <i class="bi bi-card-checklist"></i> Surveys
                </a>
            </li>
            <li>
                <a href="/Surveys" class="@(ViewContext.RouteData.Values["Page"]?.ToString() == "/TakeSurvey" ? "active" : "")">
                    <i class="bi bi-check2-square"></i> Take Survey
                </a>
            </li>
            @if (User.Identity?.IsAuthenticated == true)
            {
                <li class="logout-item">
                    <a href="/Logout" class="logout-link">
                        <i class="bi bi-box-arrow-right"></i> Logout
                    </a>
                </li>
            }
        </ul>
    </div>

    <div class="menu-overlay"></div>

    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    © 2025 - Leader Survey - All Rights Reserved
                </div>
                <div class="col-md-6 text-end">
                    <a href="#" class="text-white me-2"><i class="bi bi-github"></i></a>
                    <a href="#" class="text-white me-2"><i class="bi bi-linkedin"></i></a>
                    <a href="#" class="text-white"><i class="bi bi-twitter"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="~/lib/jquery/jquery.min.js"></script>
    <script src="~/lib/jquery-validation/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>